import React, { useEffect, useState } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Toaster } from "@/components/ui/toaster";

interface Campaign {
  id: number;
  job_title: string;
  job_description: string;
  job_budget: string | null;
  job_zip_code: string;
  job_address: string | null;
  customer_name: string | null;
  customer_email: string | null;
  customer_phone: string | null;
  search_radius: number;
  business_count: number;
  status: string;
  approved_at: string | null;
  rejected_at: string | null;
  rejection_reason: string | null;
  recipients_summary: {
    total: number;
    pending: number;
    sent: number;
    failed: number;
  };
}

type ApiResponse = {
  success: boolean;
  data?: { id: number } & Campaign;
  error?: string;
  message?: string;
};

const CampaignApprovalPage: React.FC = () => {
  const { campaignId, token } = useParams<{
    campaignId: string;
    token: string;
  }>();
  const [searchParams] = useSearchParams();
  const [campaign, setCampaign] = useState<Campaign | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState(false);
  const [showReject, setShowReject] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");
  const { toast } = useToast();

  // Check if reject parameter is in URL
  useEffect(() => {
    if (searchParams.get("reject") === "1") {
      setShowReject(true);
    }
  }, [searchParams]);

  useEffect(() => {
    if (!campaignId || !token) return;
    setLoading(true);
    setError(null);
    fetch(`/api/job-notification-campaigns/${campaignId}/token/${token}`)
      .then(async (res) => {
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }

        const text = await res.text();
        if (!text) {
          throw new Error("Empty response from server");
        }

        let data: ApiResponse;
        try {
          data = JSON.parse(text);
        } catch (parseError) {
          throw new Error("Invalid JSON response from server");
        }

        if (!data.success)
          throw new Error(data.error || "Failed to fetch campaign");
        setCampaign(data.data as Campaign);
      })
      .catch((err) => {
        console.error("Campaign fetch error:", err);
        setError(err.message || "Failed to load campaign data");
      })
      .finally(() => setLoading(false));
  }, [campaignId, token]);

  const handleApprove = async () => {
    if (!campaignId || !token) return;
    setActionLoading(true);
    setError(null);
    try {
      const res = await fetch(
        `/api/job-notification-campaigns/${campaignId}/approve/${token}`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
        }
      );

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const text = await res.text();
      if (!text) {
        throw new Error("Empty response from server");
      }

      let data: ApiResponse;
      try {
        data = JSON.parse(text);
      } catch (parseError) {
        throw new Error("Invalid JSON response from server");
      }

      if (!data.success)
        throw new Error(data.error || "Failed to approve campaign");
      setCampaign(data.data as Campaign);
      toast({
        title: "Success",
        description: "Campaign approved and notifications sent.",
      });
    } catch (err: unknown) {
      const message = err instanceof Error ? err.message : "Unknown error";
      console.error("Approve error:", err);
      setError(message);
      toast({
        title: "Error",
        description: message,
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleReject = async () => {
    if (!campaignId || !token || !rejectionReason.trim()) return;
    setActionLoading(true);
    setError(null);
    try {
      const res = await fetch(
        `/api/job-notification-campaigns/${campaignId}/reject/${token}`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ rejection_reason: rejectionReason }),
        }
      );

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const text = await res.text();
      if (!text) {
        throw new Error("Empty response from server");
      }

      let data: ApiResponse;
      try {
        data = JSON.parse(text);
      } catch (parseError) {
        throw new Error("Invalid JSON response from server");
      }

      if (!data.success)
        throw new Error(data.error || "Failed to reject campaign");
      setCampaign(data.data as Campaign);
      setShowReject(false);
      toast({ title: "Rejected", description: "Campaign rejected." });
    } catch (err: unknown) {
      const message = err instanceof Error ? err.message : "Unknown error";
      console.error("Reject error:", err);
      setError(message);
      toast({
        title: "Error",
        description: message,
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  if (loading)
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-900">
              Loading Campaign Details...
            </h2>
            <p className="text-gray-600 mt-2">
              Please wait while we fetch the campaign information.
            </p>
          </div>
        </div>
      </div>
    );

  if (error)
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <div className="text-center">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <h2 className="text-xl font-semibold mb-2">
                Error Loading Campaign
              </h2>
              <p>{error}</p>
            </div>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );

  if (!campaign)
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900">
              Campaign Not Found
            </h2>
            <p className="text-gray-600 mt-2">
              The requested campaign could not be found or may have been
              removed.
            </p>
          </div>
        </div>
      </div>
    );

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">
            JobON Campaign Approval
          </h1>
          <p className="text-gray-600 mt-2">
            Review and approve or reject this job notification campaign
          </p>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Job Notification Campaign</CardTitle>
            <div className="flex gap-2 mt-2">
              <Badge
                variant={
                  campaign.status === "approved"
                    ? "success"
                    : campaign.status === "rejected"
                    ? "destructive"
                    : "warning"
                }
              >
                {campaign.status.toUpperCase()}
              </Badge>
              {campaign.status === "approved" && campaign.approved_at && (
                <span className="text-xs text-gray-500">
                  Approved: {campaign.approved_at}
                </span>
              )}
              {campaign.status === "rejected" && campaign.rejected_at && (
                <span className="text-xs text-gray-500">
                  Rejected: {campaign.rejected_at}
                </span>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <div className="font-semibold">{campaign.job_title}</div>
              <div className="text-gray-600 mb-2">
                {campaign.job_description}
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <b>Budget:</b>{" "}
                  {campaign.job_budget
                    ? `$${campaign.job_budget}`
                    : "Not specified"}
                </div>
                <div>
                  <b>Location:</b> {campaign.job_zip_code}
                </div>
                {campaign.job_address && (
                  <div>
                    <b>Address:</b> {campaign.job_address}
                  </div>
                )}
                {campaign.customer_name && (
                  <div>
                    <b>Customer:</b> {campaign.customer_name}
                  </div>
                )}
                {campaign.customer_email && (
                  <div>
                    <b>Email:</b> {campaign.customer_email}
                  </div>
                )}
                {campaign.customer_phone && (
                  <div>
                    <b>Phone:</b> {campaign.customer_phone}
                  </div>
                )}
                <div>
                  <b>Businesses:</b> {campaign.business_count}
                </div>
                <div>
                  <b>Search radius:</b> {campaign.search_radius} miles
                </div>
              </div>
            </div>
            <div className="mb-4">
              <b>Recipients:</b> {campaign.recipients_summary.total} (Sent:{" "}
              {campaign.recipients_summary.sent}, Pending:{" "}
              {campaign.recipients_summary.pending}, Failed:{" "}
              {campaign.recipients_summary.failed})
            </div>
            {campaign.status === "rejected" && campaign.rejection_reason && (
              <div className="mb-4 text-yellow-700 bg-yellow-100 rounded p-2">
                <b>Rejection Reason:</b> {campaign.rejection_reason}
              </div>
            )}
            {campaign.status === "pending" && (
              <div className="flex gap-2 mt-4">
                <Button
                  variant="success"
                  disabled={actionLoading}
                  onClick={handleApprove}
                >
                  Approve
                </Button>
                <Button
                  variant="destructive"
                  disabled={actionLoading}
                  onClick={() => setShowReject(true)}
                >
                  Reject
                </Button>
              </div>
            )}
            {showReject && (
              <div className="mt-4">
                <Input
                  placeholder="Enter rejection reason..."
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  disabled={actionLoading}
                  className="mb-2"
                />
                <div className="flex gap-2">
                  <Button
                    variant="destructive"
                    disabled={actionLoading || !rejectionReason.trim()}
                    onClick={handleReject}
                  >
                    Confirm Reject
                  </Button>
                  <Button
                    variant="outline"
                    disabled={actionLoading}
                    onClick={() => setShowReject(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <Toaster />
    </div>
  );
};

export default CampaignApprovalPage;
