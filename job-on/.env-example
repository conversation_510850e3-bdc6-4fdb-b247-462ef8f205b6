APP_NAME=JobON
APP_ENV=local
APP_VERSION=1.0.0
APP_ID=SFBVVVZXWFNSNg==
APP_KEY=base64:uZqkE4qw6GkxZujiH1pVjTBV8Nu60eEjuh+vdlJ/xQE=
APP_DEBUG=true
APP_DEMO=0
APP_TIMEZONE=Asia/Ho_Chi_Minh
APP_URL=http://localhost:8000

# DB_HOST=db-mysql-sfo3-31802-do-user-12174955-0.b.db.ondigitalocean.com
# DB_DATABASE=job_on
# DB_USERNAME=job_on
# DB_PASSWORD=AVNS_sHVpiR-613lANzDaSfn
# DB_PORT=25060

DB_HOST=127.0.0.1
DB_DATABASE=job_on
DB_USERNAME=root
DB_PASSWORD=root_secret
DB_ROOT_PASSWORD=root_secret
DB_PORT=33061


BROADCAST_DRIVER=redis
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

# MAIL_MAILER=smtp
# MAIL_HOST=webiots.co.in
# MAIL_PORT=465
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=Fixit@12345
# MAIL_ENCRYPTION=ssl
# MAIL_FROM_ADDRESS=<EMAIL>
# MAIL_FROM_NAME="Fixit Support"

MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=pgsqacpipndhejtq
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Maid Profit"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

ASSET_URL=http://127.0.0.1:8000/

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

DUMMY_IMAGES_URL=https://laravel.pixelstrap.net/fixit/admin/assets/media.zip

FIREBASE_SERVER_API_KEY=AAAAK4QrbEo:APA91bFpA72b02unk3g7CXKtUkTdCfvQ1ryAzyV8PLFs9zW2uWcZ85YsjJOoxVmiB2eRhxxufMWi2NQ7YtmMX-AjoIaEVBdQNpEPSEplGX6xIBGD-PKRepTrCkVTTciHrS3aIU0JbS3x

GOOGLE_RECAPTCHA_SECRET=
GOOGLE_RECAPTCHA_KEY=
GOOGLE_MAP_API_KEY=AIzaSyA5zPWYmCrCFh54bW9IXr_PMNuJcIvbTVk

# PayPal
PAYPAL_MODE=1
PAYPAL_CLIENT_ID=AWSvIg3u2s-p7g2RYkcktJLjtn3Rsw0LZAm0CoS6WeYtEoYmSzRC01bT0wVxz4whG3eN4bCu1vparBbp
PAYPAL_CLIENT_SECRET=EPtAGaQiNig5iYMuxtoFs_kVimBODw7axl7hSjn21YLPi6aCRJymPoU2n9GtLWNVqXGWj155XRK7Kpcm
PAYPAL_WEBHOOK_ID=94E22264B76477432

# Stripe
STRIPE_API_KEY=pk_test_51MmTx1SHGHXeqsVlOWH2cwf42zty7jStl9ngvASN79Vri7bwGsbOSTGFTf17O2r5PiCIinh6vmO5FGrU5B2ymW7L00OcvpXwT3
STRIPE_SECRET_KEY=sk_test_51MmTx1SHGHXeqsVlAbforUpNIqByURbQy2xKZLlDrSNUvtvbgjywaaEZfGsbcQxIh0ggazGXrfnZBy0rQSLCqvzo00PyWPfbne
STRIPE_WEBHOOK_SECRET_KEY=

# RazorPay
RAZORPAY_KEY=rzp_test_iV7SM01Wb7wvhv
RAZORPAY_SECRET=gjdchqP3v7shiW7SRKo2xecV
RAZORPAY_WEBHOOK_SECRET_KEY=

# Mollie
MOLLIE_KEY=test_pKDxyTWpj6bFDuy67DBq4KHFqWSCEf
MOLLIE_WEBHOOK_URL=

# CCAvenue
CCAVENUE_SANDBOX_MODE=1
CCAVENUE_MERCHANT_ID=ENTER_YOUR_MERCHANT_ID
CCAVENUE_ACCESS_CODE=ENTER_YOUR_ACCESS_CODE
CCAVENUE_WORKING_KEY=ENTER_YOUR_WORKING_KEY

# PhonePe
PHONEPE_SANDBOX_MODE=1
PHONEPE_MERCHANT_ID=ENTER_YOUR_MERCHANT_ID
PHONEPE_SALT_KEY=
PHONEPE_SALT_INDEX=ENTER_YOUR_SALT_INDEX

# InstaMojo
INSTAMOJO_SANDBOX_MODE=1
INSTAMOJO_CLIENT_ID=ENTER_YOUR_CLIENT_ID
INSTAMOJO_CLIENT_SECRET=ENTER_YOUR_CLIENT_SECRET
INSTAMOJO_SALT_KEY=ENTER_YOUR_SALT_KEY

# bKash
BKASH_SANDBOX_MODE=1
BKASH_APP_KEY=ENTER_YOUR_APP_KEY
BKASH_APP_SECRET=ENTER_YOUR_APP_SECRET
BKASH_USERNAME=ENTER_YOUR_BKASH_USERNAME
BKASH_PASSWORD=ENTER_YOUR_BKASH_PASSWORD

# FlutterWave
FLW_SANDBOX_MOD=1
FLW_PUBLIC_KEY=ENTER_YOUR_PUBLIC_KEY
FLW_SECRET_KEY=ENTER_YOUR_SECRET_KEY
FLW_SECRET_HASH=ENTER_YOUR_SECRET_HASH

# PayStack
PAYSTACK_SANDBOX_MODE=1
PAYSTACK_PUBLIC_KEY=ENTER_YOUR_PUBLIC_KEY
PAYSTACK_SECRET_KEY=ENTER_YOUR_SECRET_KEY
PAYSTACK_PAYMENT_URL=https://api.paystack.co

# SSLCommerce
SSLC_STORE_ID=ENTER_YOUR_STORE_ID
SSLC_STORE_PASSWORD=ENTER_YOUR_STORE_PASSWORD
SSLC_SANDBOX_MODE=1
MEDIA_DISK=public

# Iyzipay
IYZIPAY_API_KEY=
IYZIPAY_SECRET_KEY=
IYZIPAY_SANDBOX_MODE=1

# Twilio
TWILIO_SID=
TWILIO_AUTH_TOKEN=
TWILIO_NUMBER=+

FIREBASE_PROJECT_ID=
FIREBASE_CLIENT_EMAIL=

GOOGLE_CLIENT_ID=243181628975-flia7298tvufdvjg2qsbn9600hog2r05.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=http://127.0.0.1:8000/auth/callback/google

LOG_CHANNEL=null
JWT_SECRET=HQ88PHjIBoVCDRE2MXC60DNamzzu7JlYz4F0oMVcROVFAmZ2sW62ZWgtXQbS4iVt

JWT_ALGO=HS256

QUEUE_CONNECTION=redis
REDIS_HOST=127.0.0.1
REDIS_PORT=63791

MONGO_ROOT_USERNAME=root
MONGO_ROOT_PASSWORD=root_secret
MONGO_DB_DATABASE=jobon_chat

# External Jobs API Configuration
EXTERNAL_JOBS_BASE_URL=https://app-api.maidprofit.com
EXTERNAL_JOBS_TIMEOUT=30

# Job Notification Configuration
JOB_NOTIFICATION_DEFAULT_RADIUS=30
JOB_NOTIFICATION_MAX_RADIUS=75
JOB_NOTIFICATION_ADMIN_EMAIL=<EMAIL>
JOB_NOTIFICATION_TOKEN_EXPIRY_HOURS=24
WEBHOOK_TOKEN=your_secure_webhook_token_here

# JobON Web App Domain for Approval Links
JOBON_WEBAPP_URL=http://localhost:8080/admin
