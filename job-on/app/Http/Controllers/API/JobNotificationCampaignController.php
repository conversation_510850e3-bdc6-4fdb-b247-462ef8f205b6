<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\JobNotificationCampaign;
use App\Http\Resources\JobNotificationCampaignResource;
use App\Jobs\ProcessJobNotificationJob;
use App\Enums\JobNotificationStatusEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class JobNotificationCampaignController extends Controller
{
    /**
     * List job notification campaigns (paginated, filterable, searchable).
     */
    public function index(Request $request)
    {
        $query = JobNotificationCampaign::with('recipients');

        // Search functionality
        if ($request->filled('search')) {
            $searchTerm = $request->input('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('job_title', 'like', '%' . $searchTerm . '%')
                  ->orWhere('job_description', 'like', '%' . $searchTerm . '%')
                  ->orWhere('customer_name', 'like', '%' . $searchTerm . '%')
                  ->orWhere('customer_email', 'like', '%' . $searchTerm . '%')
                  ->orWhere('job_zip_code', 'like', '%' . $searchTerm . '%')
                  ->orWhere('job_address', 'like', '%' . $searchTerm . '%');
            });
        }

        // Optional filters
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }
        if ($request->filled('job_id')) {
            $query->where('job_id', $request->input('job_id'));
        }
        if ($request->filled('title')) {
            $query->where('job_title', 'like', '%' . $request->input('title') . '%');
        }

        // Pagination with limit and page parameters
        $limit = $request->input('limit', 15);
        $page = $request->input('page', 1);

        // Set current page for Laravel paginator
        \Illuminate\Pagination\Paginator::currentPageResolver(function () use ($page) {
            return $page;
        });

        $campaigns = $query->orderByDesc('created_at')->paginate($limit, ['*'], 'page', $page);

        return JobNotificationCampaignResource::collection($campaigns);
    }

    /**
     * Show a single job notification campaign (with recipients summary).
     */
    public function show($id)
    {
        $campaign = JobNotificationCampaign::with('recipients')->findOrFail($id);
        return new JobNotificationCampaignResource($campaign);
    }

    /**
     * Show campaign details with token verification for approval/rejection.
     */
    public function showWithToken($id, $token)
    {
        $campaign = JobNotificationCampaign::with('recipients.business')->findOrFail($id);
        
        // Verify token
        if ($campaign->admin_token !== $token) {
            return response()->json([
                'success' => false,
                'error' => 'Invalid approval token'
            ], 403);
        }

        // Check if token is expired
        if ($campaign->token_expires_at->isPast()) {
            return response()->json([
                'success' => false,
                'error' => 'Approval token has expired'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => new JobNotificationCampaignResource($campaign)
        ]);
    }

    /**
     * Approve a job notification campaign via API.
     */
    public function approve($id, $token)
    {
        $campaign = JobNotificationCampaign::with('recipients.business')->findOrFail($id);
        
        // Verify token
        if ($campaign->admin_token !== $token) {
            return response()->json([
                'success' => false,
                'error' => 'Invalid approval token'
            ], 403);
        }

        // Check if token is expired
        if ($campaign->token_expires_at->isPast()) {
            return response()->json([
                'success' => false,
                'error' => 'Approval token has expired'
            ], 403);
        }

        // Check if campaign is in pending status
        if ($campaign->status !== JobNotificationStatusEnum::PENDING) {
            return response()->json([
                'success' => false,
                'error' => 'This campaign has already been processed',
                'data' => new JobNotificationCampaignResource($campaign)
            ], 400);
        }

        try {
            // Update campaign status
            $campaign->update([
                'status' => JobNotificationStatusEnum::APPROVED,
                'approved_at' => now(),
            ]);

            // Send notifications to businesses
            ProcessJobNotificationJob::sendNotifications($campaign);

            Log::info('Campaign approved and notifications sent', [
                'campaign_id' => $campaign->id,
                'approved_by' => 'admin_token',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Campaign has been approved and notifications have been sent to businesses',
                'data' => new JobNotificationCampaignResource($campaign->fresh('recipients.business'))
            ]);

        } catch (\Exception $e) {
            Log::error('Error approving campaign', [
                'campaign_id' => $campaign->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to approve campaign. Please contact support.',
                'data' => new JobNotificationCampaignResource($campaign)
            ], 500);
        }
    }

    /**
     * Reject a job notification campaign via API.
     */
    public function reject($id, $token, Request $request)
    {
        $campaign = JobNotificationCampaign::with('recipients.business')->findOrFail($id);
        
        // Verify token
        if ($campaign->admin_token !== $token) {
            return response()->json([
                'success' => false,
                'error' => 'Invalid approval token'
            ], 403);
        }

        // Check if token is expired
        if ($campaign->token_expires_at->isPast()) {
            return response()->json([
                'success' => false,
                'error' => 'Approval token has expired'
            ], 403);
        }

        // Check if campaign is in pending status
        if ($campaign->status !== JobNotificationStatusEnum::PENDING) {
            return response()->json([
                'success' => false,
                'error' => 'This campaign has already been processed',
                'data' => new JobNotificationCampaignResource($campaign)
            ], 400);
        }

        // Validate rejection reason
        $request->validate([
            'rejection_reason' => 'required|string|min:10|max:1000',
        ]);

        try {
            // Update campaign status
            $campaign->update([
                'status' => JobNotificationStatusEnum::REJECTED,
                'rejected_at' => now(),
                'rejection_reason' => $request->input('rejection_reason'),
            ]);

            Log::info('Campaign rejected', [
                'campaign_id' => $campaign->id,
                'rejected_by' => 'admin_token',
                'rejection_reason' => $request->input('rejection_reason'),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Campaign has been rejected',
                'data' => new JobNotificationCampaignResource($campaign->fresh('recipients.business'))
            ]);

        } catch (\Exception $e) {
            Log::error('Error rejecting campaign', [
                'campaign_id' => $campaign->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to reject campaign. Please contact support.',
                'data' => new JobNotificationCampaignResource($campaign)
            ], 500);
        }
    }
} 