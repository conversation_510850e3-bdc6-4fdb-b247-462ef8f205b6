<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\JobNotificationCampaign;
use Illuminate\Contracts\Queue\ShouldQueue;

class JobNotificationApprovalMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * The job notification campaign instance.
     *
     * @var JobNotificationCampaign
     */
    public $campaign;

    /**
     * The admin token for approval/rejection.
     *
     * @var string
     */
    public $token;

    /**
     * Create a new message instance.
     *
     * @param JobNotificationCampaign $campaign
     * @param string $token
     * @return void
     */
    public function __construct(JobNotificationCampaign $campaign, string $token)
    {
        $this->campaign = $campaign;
        $this->token = $token;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $webAppDomain = rtrim(env('JOBON_WEBAPP_URL', 'https://jobon.app/admin'), '/');
        $approveUrl = $webAppDomain . '/campaign-approval/' . $this->campaign->id . '/' . $this->token;
        $rejectUrl = $webAppDomain . '/campaign-approval/' . $this->campaign->id . '/' . $this->token . '?reject=1';
        
        // Load recipients with business information for admin review
        $recipients = $this->campaign->recipients()->with('business')->get();
        
        return $this->subject('Job Notification Approval Required')
            ->markdown('emails.job-notification-approval')
            ->with([
                'campaign' => $this->campaign,
                'recipients' => $recipients,
                'token' => $this->token,
                'approveUrl' => $approveUrl,
                'rejectUrl' => $rejectUrl,
                'expiresAt' => $this->campaign->token_expires_at->format('M d, Y H:i'),
            ]);
    }
} 